extends Area2D

@export var resource_type: String = ""
@export var max_amount: int = 100
@export var current_amount: int = 100
@export var regeneration_rate: float = 1.0  # Resources per second
@export var regeneration_enabled: bool = true

var visual_node: Node2D

func _ready():
	# Set up collision detection
	body_entered.connect(_on_body_entered)
	area_entered.connect(_on_area_entered)

	# Create visual representation
	create_visual()

	# Start regeneration timer if enabled
	if regeneration_enabled:
		var timer = Timer.new()
		timer.wait_time = 1.0
		timer.timeout.connect(_on_regeneration_timer)
		timer.autostart = true
		add_child(timer)

func create_visual():
	# Create a triangle shape for the resource
	var polygon = Polygon2D.new()

	# Set color based on resource type
	polygon.color = get_resource_color()

	# Create triangle points
	var triangle_size = 8.0
	var triangle_points = PackedVector2Array([
		Vector2(0, -triangle_size),      # top point
		Vector2(-triangle_size, triangle_size),   # bottom left
		Vector2(triangle_size, triangle_size)     # bottom right
	])
	polygon.polygon = triangle_points

	add_child(polygon)
	visual_node = polygon

	# Add collision shape
	var collision = CollisionShape2D.new()
	var shape = CircleShape2D.new()
	shape.radius = triangle_size
	collision.shape = shape
	add_child(collision)

func get_resource_color() -> Color:
	match resource_type:
		"copper":
			return Color(1, 0.5, 0)  # orange
		"iron":
			return Color(0.7, 0.7, 0.7)  # light gray
		"wood":
			return Color(0.3, 0.2, 0)  # brown
		"berries":
			return Color(1, 0, 1)  # magenta
		"gems":
			return Color(0, 1, 1)  # cyan
		_:
			return Color(1, 1, 1)  # white fallback

func get_resource_type() -> String:
	return resource_type

func collect_resource(amount: int) -> int:
	if current_amount <= 0:
		return 0

	var collected = min(amount, current_amount)
	current_amount -= collected

	# Update visual based on remaining amount
	update_visual()

	# If depleted, start regeneration or remove
	if current_amount <= 0:
		if regeneration_enabled:
			# Make visual semi-transparent to show depletion
			if visual_node:
				visual_node.modulate.a = 0.3
		else:
			# Remove the resource node
			queue_free()

	return collected

func update_visual():
	if visual_node:
		# Scale visual based on remaining amount
		var scale_factor = float(current_amount) / float(max_amount)
		scale_factor = max(0.3, scale_factor)  # Minimum scale
		visual_node.scale = Vector2(scale_factor, scale_factor)

func _on_regeneration_timer():
	if current_amount < max_amount:
		current_amount += int(regeneration_rate)
		current_amount = min(current_amount, max_amount)

		# Update visual
		update_visual()

		# Restore full opacity if regenerated
		if current_amount >= max_amount and visual_node:
			visual_node.modulate.a = 1.0

func _on_body_entered(_body):
	# Handle collision with bodies (if needed)
	pass

func _on_area_entered(_area):
	# Handle collision with other areas (if needed)
	pass

# Utility functions for world generator
func set_resource_data(type: String, amount: int = 100):
	resource_type = type
	max_amount = amount
	current_amount = amount

	if visual_node:
		visual_node.queue_free()
	create_visual()
