extends MeshInstance2D

var energy = 100
var specialization = "None"
var age = 0
var loyalty = 1
var innovation = 0.1

var move_speed = 100
var wander_timer = 0
var wander_direction = Vector2.ZERO
var priorities = ["wood", "berries", "copper"]


func _ready() -> void:
	randomize()
	$ColorRect.color = Color(randf(), randf(), randf())
	self.z_index = 10


func _process(delta: float) -> void:
	var target = find_priority_target()

	if target:
		move_towards(target.global_position, delta)
	else:
		wander(delta)


func find_priority_target():
	var sensed = $SensingArea.get_overlapping_bodies() + $SensingArea.get_overlapping_areas()

	sensed.sort_custom(self, "_sort_by_distance")

	for priority in priorities:
		for obj in sensed:
			if obj.has_method("get_resource_type") and obj.get_resource_type() == priority:
				return obj

	return null


func _sort_by_distance(a, b):
	var dist_a = global_position.distance_to(a.global_position)
	var dist_b = global_position.distance_to(b.global_position)
	return dist_a < dist_b


func move_towards(target_pos, delta):
	var direction = (target_pos - global_position).normalized()
	global_position += direction * move_speed * delta


func wander(delta):
	wander_timer -= delta
	if wander_timer <= 0:
		wander_timer = randf_range(1, 3)
		wander_direction = Vector2(randf() * 2 - 1, randf() * 2 - 1).normalized()

	global_position += wander_direction * move_speed * 0.5 * delta


func consume_energy():
	print("TODO")
