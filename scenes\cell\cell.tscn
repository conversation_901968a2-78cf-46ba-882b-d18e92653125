[gd_scene load_steps=3 format=3 uid="uid://c7kx0u11oa8cg"]

[sub_resource type="SphereMesh" id="SphereMesh_k5gl4"]

[sub_resource type="CircleShape2D" id="CircleShape2D_k5gl4"]
radius = 2.85175

[node name="Cell" type="Node2D"]

[node name="MeshInstance2D" type="MeshInstance2D" parent="."]
scale = Vector2(20, 20)
mesh = SubResource("SphereMesh_k5gl4")

[node name="SensingArea" type="Area2D" parent="MeshInstance2D"]

[node name="CollisionShape2D" type="CollisionShape2D" parent="MeshInstance2D/SensingArea"]
shape = SubResource("CircleShape2D_k5gl4")
