[plugin]

name="GDScript Formatter"
description="Using \"gdtoolkit\" to format GDScripts.
1. Require \"pip\" for install/update \"gdtoolkit\".
2. Default format shortcut is \"Shift+Alt+F\".
3. You can update gdtoolkit by:
		Project->Tool->GDScript Formatter: Install/Update gdtoolkit
4. You can change shorcut and format preference by editing Editor Settings.
5. You can change \"gdformat\" and \"pip\" command by editing Editor Settings( if these commands can't be found but are installed).
6. Format on save: default is disabled.

All preferences settings of this plugin are in the \"GDScript Formatter\" section of the Editor Settings( all projects) or Project Settings( project specific).

诚接Godot游戏开发，欢迎邮件联系。/Receive game development outsourcing, welcome to contact by email."
author="DaylilyZeleen-忘忧の(<EMAIL>)"
version="0.3.3"
script="gdscript_formatter.gd"
