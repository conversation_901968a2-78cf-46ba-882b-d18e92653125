extends Node2D
@export var world_generator_node_path: NodePath
@export var camera_node_path: NodePath
var world_generator
var camera
const CHUNK_SIZE = 32
const TILE_SIZE = 16
const LOAD_RADIUS = 2
var loaded_chunks = {}

var CellScene = preload("res://scenes/cell/cell.tscn")


func _ready():
	if world_generator_node_path != NodePath():
		world_generator = get_node(world_generator_node_path)
	if camera_node_path != NodePath():
		camera = get_node(camera_node_path)

	var cell = CellScene.instantiate()
	add_child(cell)
	cell.global_position = camera.global_position + Vector2(0, 0)

	print("Camera global position: ", camera.global_position if camera else "No Camera")
	print("Cell global position: ", cell.global_position)


func _process(delta):
	if not camera or not world_generator:
		return
	var camera_chunk_x = int(camera.position.x / (CHUNK_SIZE * TILE_SIZE))
	var camera_chunk_y = int(camera.position.y / (CHUNK_SIZE * TILE_SIZE))
	for dx in range(-LOAD_RADIUS, LOAD_RADIUS + 1):
		for dy in range(-LOAD_RADIUS, LOAD_RADIUS + 1):
			var chunk_pos = Vector2(camera_chunk_x + dx, camera_chunk_y + dy)
			if not loaded_chunks.has(chunk_pos):
				var chunk_data = world_generator.generate_chunk(chunk_pos.x, chunk_pos.y)
				loaded_chunks[chunk_pos] = chunk_data
				draw_chunk(chunk_pos, chunk_data)
	# Optional: unload far away chunks here (not implemented)


func draw_chunk(chunk_pos: Vector2, chunk_data: Array):
	var chunk_node = Node2D.new()
	chunk_node.name = "chunk_%d_%d" % [chunk_pos.x, chunk_pos.y]
	add_child(chunk_node)
	for tile in chunk_data:
		var tile_pos = tile.position * TILE_SIZE
		var color = get_color_for_terrain(tile.terrain)
		var rect = ColorRect.new()
		rect.color = color
		rect.position = tile_pos
		rect.size = Vector2(TILE_SIZE, TILE_SIZE)
		chunk_node.add_child(rect)
		# Check if resource exists and is not empty
		if tile.resource != null and tile.resource != "":
			var resource_triangle = Polygon2D.new()
			resource_triangle.color = get_color_for_resource(tile.resource)

			# Create triangle points centered in the tile
			var triangle_size = TILE_SIZE / 2
			var center = tile_pos + Vector2(TILE_SIZE / 2, TILE_SIZE / 2)
			var triangle_points = PackedVector2Array(
				[center + Vector2(0, -triangle_size / 2), center + Vector2(-triangle_size / 2, triangle_size / 2), center + Vector2(triangle_size / 2, triangle_size / 2)]  # top point  # bottom left  # bottom right
			)
			resource_triangle.polygon = triangle_points
			chunk_node.add_child(resource_triangle)


func get_color_for_terrain(terrain: String) -> Color:
	match terrain:
		"water":
			return Color(0, 0, 1)  # blue
		"sand":
			return Color(1, 0.9, 0.6)  # sand yellow
		"grassland":
			return Color(0, 1, 0)  # green
		"forest":
			return Color(0, 0.5, 0)  # dark green
		"mountain":
			return Color(0.5, 0.5, 0.5)  # gray
		_:
			return Color(1, 1, 1)  # white fallback


func get_color_for_resource(resource: String) -> Color:
	match resource:
		"copper":
			return Color(1, 0.5, 0)  # orange
		"wood":
			return Color(0.3, 0.2, 0)  # brown
		"berries":
			return Color(1, 0, 1)  # magenta
		_:
			return Color(1, 1, 1)  # white fallback
