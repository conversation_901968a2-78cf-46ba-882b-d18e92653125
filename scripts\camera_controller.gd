extends Camera2D

# Camera movement settings
@export var max_speed: float = 300.0  # Maximum camera movement speed
@export var edge_threshold: float = 50.0  # Distance from edge to start moving
@export var acceleration: float = 5.0  # How quickly camera accelerates
@export var deceleration: float = 8.0  # How quickly camera stops

# Internal variables
var current_velocity: Vector2 = Vector2.ZERO
var screen_size: Vector2
var mouse_pos: Vector2


func _ready():
	# Get the viewport size
	screen_size = get_viewport().get_visible_rect().size

	# Make sure the camera is current
	make_current()


func _process(delta):
	# Get current mouse position
	mouse_pos = get_viewport().get_mouse_position()

	# Calculate target velocity based on mouse position
	var target_velocity = calculate_camera_velocity()

	# Smoothly interpolate to target velocity
	if target_velocity.length() > 0:
		current_velocity = current_velocity.move_toward(target_velocity, acceleration * max_speed * delta)
	else:
		current_velocity = current_velocity.move_toward(Vector2.ZERO, deceleration * max_speed * delta)

	# Move the camera
	global_position += current_velocity * delta


func calculate_camera_velocity() -> Vector2:
	var velocity = Vector2.ZERO

	# Check horizontal movement (left/right edges)
	if mouse_pos.x <= edge_threshold:
		# Mouse near left edge - move left
		var intensity = 1.0 - (mouse_pos.x / edge_threshold)
		velocity.x = -intensity * max_speed
	elif mouse_pos.x >= screen_size.x - edge_threshold:
		# Mouse near right edge - move right
		var distance_from_edge = screen_size.x - mouse_pos.x
		var intensity = 1.0 - (distance_from_edge / edge_threshold)
		velocity.x = intensity * max_speed

	# Check vertical movement (top/bottom edges)
	if mouse_pos.y <= edge_threshold:
		# Mouse near top edge - move up
		var intensity = 1.0 - (mouse_pos.y / edge_threshold)
		velocity.y = -intensity * max_speed
	elif mouse_pos.y >= screen_size.y - edge_threshold:
		# Mouse near bottom edge - move down
		var distance_from_edge = screen_size.y - mouse_pos.y
		var intensity = 1.0 - (distance_from_edge / edge_threshold)
		velocity.y = intensity * max_speed

	return velocity


# Optional: Add camera bounds to prevent moving too far
@export var use_bounds: bool = false
@export var bounds_rect: Rect2 = Rect2(-1000, -1000, 2000, 2000)


func _process_with_bounds(delta):
	if not use_bounds:
		_process(delta)
		return

	# Store old position
	var old_pos = global_position

	# Do normal movement
	_process(delta)

	# Clamp to bounds
	global_position.x = clamp(global_position.x, bounds_rect.position.x, bounds_rect.position.x + bounds_rect.size.x)
	global_position.y = clamp(global_position.y, bounds_rect.position.y, bounds_rect.position.y + bounds_rect.size.y)

	# If we hit a boundary, stop velocity in that direction
	if global_position.x != old_pos.x and abs(global_position.x - old_pos.x) < current_velocity.x * delta:
		current_velocity.x = 0
	if global_position.y != old_pos.y and abs(global_position.y - old_pos.y) < current_velocity.y * delta:
		current_velocity.y = 0


# Override _process if you want to use bounds
func _ready_with_bounds():
	_ready()
	if use_bounds:
		set_process(false)
		set_physics_process(true)


func _physics_process(delta):
	if use_bounds:
		_process_with_bounds(delta)
