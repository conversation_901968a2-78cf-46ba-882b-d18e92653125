[gd_scene load_steps=4 format=3 uid="uid://bcvkffyrgrbpm"]

[ext_resource type="Script" uid="uid://c4rmbhexe32t1" path="res://scripts/world.gd" id="1_jone5"]
[ext_resource type="Script" uid="uid://b5eg2lib6m40m" path="res://scripts/world_generator.gd" id="2_s3tw4"]
[ext_resource type="Script" uid="uid://dnaon78en1lld" path="res://scripts/camera_controller.gd" id="3_1mh63"]

[node name="root" type="Node2D"]

[node name="Camera2D" type="Camera2D" parent="."]
script = ExtResource("3_1mh63")
max_speed = 200.0

[node name="GameManager" type="Node2D" parent="."]

[node name="WorldGenerator" type="Node2D" parent="GameManager"]
script = ExtResource("2_s3tw4")
world_generator_node_path = NodePath("../World")
camera_node_path = NodePath("../../Camera2D")

[node name="World" type="Node2D" parent="GameManager"]
script = ExtResource("1_jone5")
