extends Node2D

const CHUNK_SIZE = 32
const TILE_SIZE = 16

var terrain_noise: OpenSimplexNoise
var resource_noise: OpenSimplexNoise
var moisture_noise: OpenSimplexNoise  # New noise for more variety


func _ready():
	# Initialize custom OpenSimplex noise generators
	terrain_noise = OpenSimplexNoise.new()
	terrain_noise.set_seed(randi())
	terrain_noise.set_frequency(0.015)  # Reduced for larger terrain features
	terrain_noise.set_octaves(6)  # More octaves for detail
	terrain_noise.set_gain(0.5)
	terrain_noise.set_lacunarity(2.0)

	resource_noise = OpenSimplexNoise.new()
	resource_noise.set_seed(randi() + 1000)
	resource_noise.set_frequency(0.05)
	resource_noise.set_octaves(3)
	resource_noise.set_gain(0.6)
	resource_noise.set_lacunarity(2.0)

	# Additional noise for terrain variation
	moisture_noise = OpenSimplexNoise.new()
	moisture_noise.set_seed(randi() + 2000)
	moisture_noise.set_frequency(0.03)
	moisture_noise.set_octaves(4)
	moisture_noise.set_gain(0.4)
	moisture_noise.set_lacunarity(1.8)


func generate_chunk(chunk_x: int, chunk_y: int) -> Array:
	var chunk_data = []
	for local_x in range(CHUNK_SIZE):
		for local_y in range(CHUNK_SIZE):
			var world_x = chunk_x * CHUNK_SIZE + local_x
			var world_y = chunk_y * CHUNK_SIZE + local_y

			# Get terrain height noise [-1,1]
			var height = terrain_noise.get_noise_2d(world_x, world_y)

			# Get moisture for additional terrain variation
			var moisture = moisture_noise.get_noise_2d(world_x, world_y)

			# Determine terrain type using improved logic
			var terrain = determine_terrain_type(height, moisture)

			# Get resource noise value [-1,1]
			var resource_val = resource_noise.get_noise_2d(world_x, world_y)
			# Adjust resource value to [0,1] range
			resource_val = (resource_val + 1.0) * 0.5

			# Determine resource type based on terrain and adjusted resource value
			var resource = determine_resource_type(terrain, resource_val)

			# Store tile info as a dictionary
			var tile = {"position": Vector2(world_x, world_y), "terrain": terrain, "resource": resource}
			chunk_data.append(tile)

	return chunk_data


func determine_terrain_type(height: float, moisture: float) -> String:
	# Use raw noise values [-1,1] for better distribution

	# Water forms at low elevations
	if height < -0.3:
		return "water"

	# Coastal areas - sand near water with some randomness
	elif height < -0.1:
		# Sometimes sand, sometimes water for varied coastlines
		if moisture > 0.2:
			return "sand"
		else:
			return "water"

	# Low elevation areas
	elif height < 0.1:
		if moisture < -0.3:
			return "sand"  # Dry areas become sand/desert
		else:
			return "grassland"

	# Medium elevation
	elif height < 0.4:
		if moisture > 0.0:
			return "forest"  # Moist areas become forest
		else:
			return "grassland"

	# High elevation - mountains are more common now
	elif height < 0.7:
		if moisture > 0.3:
			return "forest"  # High altitude forests
		else:
			return "mountain"  # Rocky areas

	# Very high elevation - always mountains
	else:
		return "mountain"


func determine_resource_type(terrain: String, resource_val: float) -> String:
	# Adjusted thresholds for better resource distribution
	if terrain == "mountain" and resource_val > 0.5:  # More common
		if resource_val > 0.8:
			return "iron"  # Rare, valuable resource
		else:
			return "copper"
	elif terrain == "forest" and resource_val > 0.4:  # More common
		return "wood"
	elif terrain == "grassland" and resource_val > 0.6:
		return "berries"
	elif terrain == "sand" and resource_val > 0.8:  # Rare desert resources
		return "gems"
	else:
		return ""


# Custom OpenSimplex Noise Implementation
class OpenSimplexNoise:
	var perm: Array = []
	var perm_grad_index_3d: Array = []
	var seed_value: int = 0
	var frequency: float = 1.0
	var octaves: int = 1
	var gain: float = 0.5
	var lacunarity: float = 2.0

	# Gradient vectors for 2D
	var grad2: Array = [
		Vector2(0.130526192220052, 0.99144486137381),
		Vector2(0.38268343236509, 0.923879532511287),
		Vector2(0.608761429008721, 0.793353340291235),
		Vector2(0.793353340291235, 0.608761429008721),
		Vector2(0.923879532511287, 0.38268343236509),
		Vector2(0.99144486137381, 0.130526192220051),
		Vector2(0.99144486137381, -0.130526192220051),
		Vector2(0.923879532511287, -0.38268343236509),
		Vector2(0.793353340291235, -0.60876142900872),
		Vector2(0.608761429008721, -0.793353340291235),
		Vector2(0.38268343236509, -0.923879532511287),
		Vector2(0.130526192220052, -0.99144486137381),
		Vector2(-0.130526192220052, -0.99144486137381),
		Vector2(-0.38268343236509, -0.923879532511287),
		Vector2(-0.608761429008721, -0.793353340291235),
		Vector2(-0.793353340291235, -0.608761429008721),
		Vector2(-0.923879532511287, -0.38268343236509),
		Vector2(-0.99144486137381, -0.130526192220052),
		Vector2(-0.99144486137381, 0.130526192220051),
		Vector2(-0.923879532511287, 0.38268343236509),
		Vector2(-0.793353340291235, 0.608761429008721),
		Vector2(-0.608761429008721, 0.793353340291235),
		Vector2(-0.38268343236509, 0.923879532511287),
		Vector2(-0.130526192220052, 0.99144486137381)
	]

	func _init():
		set_seed(0)

	func set_seed(new_seed: int):
		seed_value = new_seed
		var rng = RandomNumberGenerator.new()
		rng.seed = new_seed

		# Initialize permutation table
		perm = []
		for i in range(256):
			perm.append(i)

		# Shuffle the permutation table
		for i in range(255, 0, -1):
			var j = rng.randi() % (i + 1)
			var temp = perm[i]
			perm[i] = perm[j]
			perm[j] = temp

		# Duplicate for easier indexing
		for i in range(256):
			perm.append(perm[i])

		# Initialize gradient index table
		perm_grad_index_3d = []
		for i in range(512):
			perm_grad_index_3d.append(perm[i] % grad2.size())

	func set_frequency(new_frequency: float):
		frequency = new_frequency

	func set_octaves(new_octaves: int):
		octaves = new_octaves

	func set_gain(new_gain: float):
		gain = new_gain

	func set_lacunarity(new_lacunarity: float):
		lacunarity = new_lacunarity

	func get_noise_2d(x: float, y: float) -> float:
		if octaves == 1:
			return _get_single_noise_2d(x * frequency, y * frequency)

		var sum = 0.0
		var amplitude = 1.0
		var freq = frequency
		var max_value = 0.0

		for i in range(octaves):
			sum += _get_single_noise_2d(x * freq, y * freq) * amplitude
			max_value += amplitude
			amplitude *= gain
			freq *= lacunarity

		return sum / max_value

	func _get_single_noise_2d(x: float, y: float) -> float:
		# Skewing and unskewing factors for 2D
		var F2 = 0.5 * (sqrt(3.0) - 1.0)
		var G2 = (3.0 - sqrt(3.0)) / 6.0

		var n0: float
		var n1: float
		var n2: float

		# Skew the input space to determine which simplex cell we're in
		var s = (x + y) * F2
		var i = int(floor(x + s))
		var j = int(floor(y + s))
		var t = (i + j) * G2
		var X0 = i - t
		var Y0 = j - t
		var x0 = x - X0
		var y0 = y - Y0

		# Determine which simplex we are in
		var i1: int
		var j1: int
		if x0 > y0:
			i1 = 1
			j1 = 0
		else:
			i1 = 0
			j1 = 1

		# Offsets for second corner of simplex
		var x1 = x0 - i1 + G2
		var y1 = y0 - j1 + G2
		var x2 = x0 - 1.0 + 2.0 * G2
		var y2 = y0 - 1.0 + 2.0 * G2

		# Work out the hashed gradient indices of the three simplex corners
		var ii = i & 255
		var jj = j & 255
		var gi0 = perm_grad_index_3d[ii + perm[jj]]
		var gi1 = perm_grad_index_3d[ii + i1 + perm[jj + j1]]
		var gi2 = perm_grad_index_3d[ii + 1 + perm[jj + 1]]

		# Calculate the contribution from the three corners
		var t0 = 0.5 - x0 * x0 - y0 * y0
		if t0 < 0:
			n0 = 0.0
		else:
			t0 *= t0
			n0 = t0 * t0 * (grad2[gi0].x * x0 + grad2[gi0].y * y0)

		var t1 = 0.5 - x1 * x1 - y1 * y1
		if t1 < 0:
			n1 = 0.0
		else:
			t1 *= t1
			n1 = t1 * t1 * (grad2[gi1].x * x1 + grad2[gi1].y * y1)

		var t2 = 0.5 - x2 * x2 - y2 * y2
		if t2 < 0:
			n2 = 0.0
		else:
			t2 *= t2
			n2 = t2 * t2 * (grad2[gi2].x * x2 + grad2[gi2].y * y2)

		# Add contributions from each corner to get the final noise value
		# The result is scaled to return values in the interval [-1,1]
		return 70.0 * (n0 + n1 + n2)
